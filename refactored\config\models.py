"""
AI Model Configuration for BlendPro
Defines available models and their capabilities
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class ModelCapability(Enum):
    """Model capabilities enumeration"""
    TEXT_GENERATION = "text_generation"
    VISION = "vision"
    CODE_GENERATION = "code_generation"
    FUNCTION_CALLING = "function_calling"
    LONG_CONTEXT = "long_context"

@dataclass
class ModelConfig:
    """Configuration for an AI model"""
    name: str
    display_name: str
    provider: str
    capabilities: List[ModelCapability]
    max_tokens: int
    context_window: int
    cost_per_1k_tokens: float
    recommended_temperature: float = 0.7
    supports_streaming: bool = True
    
    def has_capability(self, capability: ModelCapability) -> bool:
        """Check if model has specific capability"""
        return capability in self.capabilities
    
    def is_vision_capable(self) -> bool:
        """Check if model supports vision"""
        return self.has_capability(ModelCapability.VISION)
    
    def is_code_capable(self) -> bool:
        """Check if model is good for code generation"""
        return self.has_capability(ModelCapability.CODE_GENERATION)

# Predefined model configurations
AVAILABLE_MODELS: Dict[str, ModelConfig] = {
    # OpenAI Models
    "gpt-4": ModelConfig(
        name="gpt-4",
        display_name="GPT-4",
        provider="openai",
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.FUNCTION_CALLING
        ],
        max_tokens=4096,
        context_window=8192,
        cost_per_1k_tokens=0.03,
        recommended_temperature=0.7
    ),
    
    "gpt-4-turbo": ModelConfig(
        name="gpt-4-turbo",
        display_name="GPT-4 Turbo",
        provider="openai",
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.LONG_CONTEXT
        ],
        max_tokens=4096,
        context_window=128000,
        cost_per_1k_tokens=0.01,
        recommended_temperature=0.7
    ),
    
    "gpt-4o": ModelConfig(
        name="gpt-4o",
        display_name="GPT-4o",
        provider="openai",
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.LONG_CONTEXT
        ],
        max_tokens=4096,
        context_window=128000,
        cost_per_1k_tokens=0.005,
        recommended_temperature=0.7
    ),
    
    "gpt-4o-mini": ModelConfig(
        name="gpt-4o-mini",
        display_name="GPT-4o Mini",
        provider="openai",
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.FUNCTION_CALLING
        ],
        max_tokens=16384,
        context_window=128000,
        cost_per_1k_tokens=0.00015,
        recommended_temperature=0.7
    ),
    
    "gpt-4-vision-preview": ModelConfig(
        name="gpt-4-vision-preview",
        display_name="GPT-4 Vision Preview",
        provider="openai",
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION
        ],
        max_tokens=4096,
        context_window=128000,
        cost_per_1k_tokens=0.01,
        recommended_temperature=0.7
    ),
    
    # Claude Models
    "claude-3-5-sonnet-20241022": ModelConfig(
        name="claude-3-5-sonnet-20241022",
        display_name="Claude 3.5 Sonnet",
        provider="anthropic",
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.LONG_CONTEXT
        ],
        max_tokens=8192,
        context_window=200000,
        cost_per_1k_tokens=0.003,
        recommended_temperature=0.7
    ),
    
    "claude-3-opus-20240229": ModelConfig(
        name="claude-3-opus-20240229",
        display_name="Claude 3 Opus",
        provider="anthropic",
        capabilities=[
            ModelCapability.TEXT_GENERATION,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.LONG_CONTEXT
        ],
        max_tokens=4096,
        context_window=200000,
        cost_per_1k_tokens=0.015,
        recommended_temperature=0.7
    )
}

def get_model_config(model_name: str) -> Optional[ModelConfig]:
    """Get configuration for a specific model"""
    return AVAILABLE_MODELS.get(model_name)

def get_models_by_capability(capability: ModelCapability) -> List[ModelConfig]:
    """Get all models that have a specific capability"""
    return [
        config for config in AVAILABLE_MODELS.values()
        if config.has_capability(capability)
    ]

def get_vision_models() -> List[ModelConfig]:
    """Get all models that support vision"""
    return get_models_by_capability(ModelCapability.VISION)

def get_code_models() -> List[ModelConfig]:
    """Get all models that are good for code generation"""
    return get_models_by_capability(ModelCapability.CODE_GENERATION)

def get_model_choices() -> List[tuple]:
    """Get model choices for Blender enum property"""
    return [
        (name, config.display_name, f"Use {config.display_name}")
        for name, config in AVAILABLE_MODELS.items()
    ]

def get_vision_model_choices() -> List[tuple]:
    """Get vision model choices for Blender enum property"""
    return [
        (name, config.display_name, f"Use {config.display_name} for vision")
        for name, config in AVAILABLE_MODELS.items()
        if config.is_vision_capable()
    ]
