"""
BlendPro: AI Co-Pilot for Blender
Advanced AI-powered Blender assistant with intelligent interaction capabilities

Author: inkbytefo
Version: 4.0.0 (Refactored)
"""

import sys
import os
import bpy

# Add the 'lib' folder to the Python path
libs_path = os.path.join(os.path.dirname(os.path.realpath(__file__)), "..", "lib")
if libs_path not in sys.path:
    sys.path.append(libs_path)

# Import core modules
from .config import settings
from .utils import api_client, backup_manager, code_executor, file_manager
from .core import interaction_engine, conversation_memory
from .vision import scene_analyzer
from .workflow import scene_monitor, proactive_suggestions
from .ui import main_panel, settings_panel

# Import Blender info
from .bl_info import bl_info

# Global availability flags
VISION_SYSTEM_AVAILABLE = True
SCENE_MONITOR_AVAILABLE = True

try:
    from .vision import multi_modal_vision, screenshot_manager
    print("BlendPro Vision System loaded successfully")
except ImportError as e:
    print(f"Vision System not available: {e}")
    VISION_SYSTEM_AVAILABLE = False
except Exception as e:
    print(f"Error loading Vision System: {e}")
    VISION_SYSTEM_AVAILABLE = False

try:
    from .workflow import auto_fix_system, action_library
    print("BlendPro Workflow System loaded successfully")
except ImportError as e:
    print(f"Workflow System not available: {e}")
    SCENE_MONITOR_AVAILABLE = False
except Exception as e:
    print(f"Error loading Workflow System: {e}")
    SCENE_MONITOR_AVAILABLE = False

def register():
    """Register all Blender classes and properties"""
    # Initialize properties first
    from .utils.file_manager import init_props
    init_props()
    
    # Register UI components
    from .ui import main_panel, settings_panel, chat_interface, interactive_messages
    main_panel.register()
    settings_panel.register()
    chat_interface.register()
    interactive_messages.register()
    
    # Register core operators
    from .core import interaction_engine
    interaction_engine.register()
    
    # Register workflow operators
    if SCENE_MONITOR_AVAILABLE:
        from .workflow import scene_monitor, auto_fix_system
        scene_monitor.register()
        auto_fix_system.register()
    
    # Register vision operators
    if VISION_SYSTEM_AVAILABLE:
        from .vision import screenshot_manager
        screenshot_manager.register()
    
    print("BlendPro: AI Co-Pilot registered successfully")

def unregister():
    """Unregister all Blender classes and properties"""
    # Unregister in reverse order
    if VISION_SYSTEM_AVAILABLE:
        from .vision import screenshot_manager
        screenshot_manager.unregister()
    
    if SCENE_MONITOR_AVAILABLE:
        from .workflow import scene_monitor, auto_fix_system
        auto_fix_system.unregister()
        scene_monitor.unregister()
    
    from .core import interaction_engine
    interaction_engine.unregister()
    
    from .ui import main_panel, settings_panel, chat_interface, interactive_messages
    interactive_messages.unregister()
    chat_interface.unregister()
    settings_panel.unregister()
    main_panel.unregister()
    
    # Clear properties
    from .utils.file_manager import clear_props
    clear_props()
    
    print("BlendPro: AI Co-Pilot unregistered successfully")

if __name__ == "__main__":
    register()
